<?php

namespace Domain\Stats\Modules;

use App\Traits\Filters\DateFilterTrait;
use Illuminate\Database\Eloquent\Builder;

/*********************************************************************
 * OUTREACH STATS FILTER TRAIT
 **********************************************************************
 *
 * Specialized filtering functionality for outreach statistics.
 * Extends DateFilterTrait with outreach-specific date column mapping
 * based on outreach status.
 *
 * USAGE:
 * - $this->applyOutreachDateFilter($query, $filters, 'inprogress')
 * - $this->getDateColumnForStatus('onboarded')
 *
 *********************************************************************/
trait OutreachStatsFilterTrait
{
    use DateFilterTrait;




    /*********************************************************************
     * APPLY OUTREACH DATE FILTER
     **********************************************************************
     *
     * Apply date filter to outreach query with status-specific date
     * column mapping. Each outreach status uses different timestamp
     * columns for accurate filtering.
     *
     * @param Builder $query   - The query builder instance to filter
     * @param array   $filters - Filter array containing date parameters
     * @param string  $status  - Outreach status (inprogress, onboarded, rejected)
     * @return Builder - Filtered query builder
     *
     *********************************************************************/
    protected function applyOutreachDateFilter(Builder $query, array $filters, string $status): Builder
    {
        $dateColumn = $this->getDateColumnForStatus($status);
        
        return $this->applyDateFilter($query, $filters, $dateColumn);
    }




    /*********************************************************************
     * GET DATE COLUMN FOR STATUS
     **********************************************************************
     *
     * Maps outreach status to appropriate database timestamp column.
     * Different statuses track different milestone dates.
     *
     * @param string $status - Outreach status identifier
     * @return string - Database column name for date filtering
     *
     *********************************************************************/
    protected function getDateColumnForStatus(string $status): string
    {
        return match ($status) {
            'inprogress' => 'created_at',
            'onboarded' => 'onboarded_at', 
            'rejected' => 'rejected_at',
            default => 'created_at',
        };
    }
}
