<?php

namespace Domain\Stats\Lists;

use App\Enums\Role;
use App\Models\User;
use Domain\Stats\Modules\OutreachStatsFilterTrait;
use Illuminate\Pagination\LengthAwarePaginator;

/*********************************************************************
 * GET OUTREACH USERS WITH STATS
 **********************************************************************
 *
 * Retrieves paginated outreach users with their performance statistics.
 * Applies date filtering to outreach counts based on status-specific
 * timestamp columns for accurate reporting.
 *
 * RESPONSIBILITIES:
 * - Query users with outreach role
 * - Apply date filters using OutreachStatsFilterTrait
 * - Calculate status-specific counts (inprogress, onboarded, rejected)
 * - Handle pagination with query string preservation
 * - Optimize database queries to avoid N+1 problems
 *
 *********************************************************************/
class getOutreachUsersWithStats
{
    use OutreachStatsFilterTrait;




    /*********************************************************************
     * HANDLE REQUEST
     **********************************************************************
     *
     * Main method to retrieve filtered and paginated outreach users
     * with their statistics. Applies date filtering per outreach status
     * and returns paginated results.
     *
     * @param array $filters - Filter parameters from request
     * @return LengthAwarePaginator - Paginated users with stats
     *
     *********************************************************************/
    public function handle(array $filters): LengthAwarePaginator
    {
        // -----------------------
        // Initialize Base Query
        $query = User::where('role', Role::Outreach->value);


        // -----------------------
        // Add Outreach Counts with Date Filtering
        $query->withCount([
            'outreaches as inprogress_count' => function ($q) use ($filters) {
                $q->where('status', 'inprogress');
                $this->applyOutreachDateFilter($q, $filters, 'inprogress');
            },
            'outreaches as onboarded_count' => function ($q) use ($filters) {
                $q->where('status', 'onboarded');
                $this->applyOutreachDateFilter($q, $filters, 'onboarded');
            },
            'outreaches as rejected_count' => function ($q) use ($filters) {
                $q->where('status', 'rejected');
                $this->applyOutreachDateFilter($q, $filters, 'rejected');
            },
        ]);


        // -----------------------
        // Apply Sorting
        $query->orderByDesc('id');


        // -----------------------
        // Paginate Results
        return $query->paginate(10)->withQueryString();
    }
}
